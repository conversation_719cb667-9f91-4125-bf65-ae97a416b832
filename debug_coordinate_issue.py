#!/usr/bin/env python3
"""
Debug script to understand the exact coordinate transformation issue.

This script will help us understand why the crosshair placement is still offset
even after the previous fix attempt.
"""

import sys
from PyQt6.QtWidgets import QApplication, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt, QPointF, QPoint
from PyQt6.QtGui import QPixmap, QMouseEvent, QPainter, QColor, QPen

class DebugCoordinateView(QGraphicsView):
    """Debug view to understand coordinate transformation issues."""
    
    def __init__(self):
        super().__init__()
        self.setMouseTracking(True)
        
        # Create a test scene with a precise grid
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # Create a test image with numbered pixels
        self.create_debug_image()
        
        # Set initial zoom to see individual pixels clearly
        self.scale(10, 10)  # 10x zoom to see pixels clearly
        
    def create_debug_image(self):
        """Create a debug image with clearly marked pixels."""
        # Create a 20x20 test image
        pixmap = QPixmap(20, 20)
        pixmap.fill(QColor(255, 255, 255))  # White background
        
        painter = QPainter(pixmap)
        
        # Draw each pixel with a different color pattern
        for y in range(20):
            for x in range(20):
                if x == 10 and y == 10:
                    # Center pixel - bright red
                    painter.fillRect(x, y, 1, 1, QColor(255, 0, 0))
                elif x % 2 == 0 and y % 2 == 0:
                    # Even coordinates - blue
                    painter.fillRect(x, y, 1, 1, QColor(0, 0, 255))
                elif x % 2 == 1 and y % 2 == 1:
                    # Odd coordinates - green
                    painter.fillRect(x, y, 1, 1, QColor(0, 255, 0))
                else:
                    # Mixed - gray
                    painter.fillRect(x, y, 1, 1, QColor(128, 128, 128))
        
        painter.end()
        
        # Add to scene
        pixmap_item = QGraphicsPixmapItem(pixmap)
        self.scene.addItem(pixmap_item)
        self.scene.setSceneRect(0, 0, 20, 20)
        
    def mousePressEvent(self, event: QMouseEvent):
        """Debug coordinate transformation on click."""
        if event.button() == Qt.MouseButton.RightButton:
            print("\n" + "="*60)
            print("COORDINATE TRANSFORMATION DEBUG")
            print("="*60)
            
            # Get raw mouse position
            mouse_pos = event.position()
            print(f"1. Raw mouse position: ({mouse_pos.x():.6f}, {mouse_pos.y():.6f})")
            
            # Method 1: Current "fixed" method (still buggy)
            mouse_point = QPointF(mouse_pos.x(), mouse_pos.y())
            scene_pos_method1 = self.mapToScene(mouse_point.toPoint())
            print(f"2. Method 1 (QPointF.toPoint()): ({scene_pos_method1.x():.6f}, {scene_pos_method1.y():.6f})")
            
            # Method 2: Direct toPoint() (original buggy method)
            scene_pos_method2 = self.mapToScene(event.position().toPoint())
            print(f"3. Method 2 (direct toPoint()): ({scene_pos_method2.x():.6f}, {scene_pos_method2.y():.6f})")
            
            # Method 3: Use integer conversion
            scene_pos_method3 = self.mapToScene(int(mouse_pos.x()), int(mouse_pos.y()))
            print(f"4. Method 3 (int conversion): ({scene_pos_method3.x():.6f}, {scene_pos_method3.y():.6f})")
            
            # Method 4: Use QPoint constructor
            qpoint = QPoint(int(mouse_pos.x()), int(mouse_pos.y()))
            scene_pos_method4 = self.mapToScene(qpoint)
            print(f"5. Method 4 (QPoint constructor): ({scene_pos_method4.x():.6f}, {scene_pos_method4.y():.6f})")
            
            # Method 5: Manual calculation using transform matrix
            transform = self.transform()
            viewport_center = self.viewport().rect().center()
            scene_center = self.mapToScene(viewport_center)
            
            # Calculate scene position manually
            manual_scene_x = (mouse_pos.x() - viewport_center.x()) / transform.m11() + scene_center.x()
            manual_scene_y = (mouse_pos.y() - viewport_center.y()) / transform.m22() + scene_center.y()
            scene_pos_method5 = QPointF(manual_scene_x, manual_scene_y)
            print(f"6. Method 5 (manual calculation): ({scene_pos_method5.x():.6f}, {scene_pos_method5.y():.6f})")
            
            # Show differences
            print("\nDIFFERENCES FROM METHOD 5 (manual):")
            for i, (name, pos) in enumerate([
                ("Method 1", scene_pos_method1),
                ("Method 2", scene_pos_method2), 
                ("Method 3", scene_pos_method3),
                ("Method 4", scene_pos_method4)
            ], 1):
                diff_x = pos.x() - scene_pos_method5.x()
                diff_y = pos.y() - scene_pos_method5.y()
                print(f"   {name}: ({diff_x:+.6f}, {diff_y:+.6f})")
            
            # Show pixel targeting
            print("\nPIXEL TARGETING:")
            for i, (name, pos) in enumerate([
                ("Method 1", scene_pos_method1),
                ("Method 2", scene_pos_method2),
                ("Method 3", scene_pos_method3),
                ("Method 4", scene_pos_method4),
                ("Method 5", scene_pos_method5)
            ], 1):
                target_x = int(round(pos.x()))
                target_y = int(round(pos.y()))
                snapped_x = round(pos.x()) + 0.5
                snapped_y = round(pos.y()) + 0.5
                print(f"   {name}: pixel ({target_x}, {target_y}), snapped ({snapped_x:.1f}, {snapped_y:.1f})")
            
            # Show transform info
            print(f"\nTRANSFORM INFO:")
            print(f"   Scale: ({transform.m11():.6f}, {transform.m22():.6f})")
            print(f"   Translation: ({transform.dx():.6f}, {transform.dy():.6f})")
            print(f"   Viewport center: ({viewport_center.x()}, {viewport_center.y()})")
            print(f"   Scene center: ({scene_center.x():.6f}, {scene_center.y():.6f})")
            
        super().mousePressEvent(event)

class DebugWindow(QWidget):
    """Main window for coordinate debugging."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Coordinate Transformation Debug")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("""
Coordinate Transformation Debug

This will help us understand exactly what's happening with coordinate conversion.

Instructions:
1. Right-click anywhere on the colored grid
2. Check the console output to see all coordinate transformation methods
3. Look for differences between methods to identify the issue

The grid is zoomed 10x so you can see individual pixels clearly.
Red pixel is at (10,10), blue pixels are at even coordinates, green at odd coordinates.
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Debug view
        self.debug_view = DebugCoordinateView()
        layout.addWidget(self.debug_view)
        
        self.setLayout(layout)

def main():
    """Run the coordinate debug test."""
    app = QApplication(sys.argv)
    
    window = DebugWindow()
    window.show()
    
    print("Coordinate Transformation Debug Started")
    print("Right-click on the grid to see detailed coordinate analysis")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
