#!/usr/bin/env python3
"""
Test script to investigate the rounding issue in pixel targeting.

This script will help us understand if the issue is with the rounding logic
rather than coordinate conversion.
"""

import sys
import math
from PyQt6.QtWidgets import QApplication, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QVBoxLayout, QWidget, QLabel
from PyQt6.QtCore import Qt, QPointF, QPoint
from PyQt6.QtGui import QPixmap, QMouseEvent, QPainter, QColor, QPen
from PyQt6.QtWidgets import QGraphicsLineItem, QGraphicsRectItem

class RoundingTestView(QGraphicsView):
    """Test view to investigate rounding issues in pixel targeting."""
    
    def __init__(self):
        super().__init__()
        self.setMouseTracking(True)
        
        # Create a test scene with clearly marked pixel boundaries
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        
        # Create a test image with pixel grid
        self.create_pixel_grid()
        
        # Set zoom to see pixels clearly
        self.scale(20, 20)  # 20x zoom
        
        # Crosshair elements
        self.crosshair_horizontal = None
        self.crosshair_vertical = None
        self.crosshair_center_square = None
        
    def create_pixel_grid(self):
        """Create a pixel grid with clear boundaries."""
        # Create a 10x10 test image
        pixmap = QPixmap(10, 10)
        pixmap.fill(QColor(255, 255, 255))  # White background
        
        painter = QPainter(pixmap)
        
        # Draw each pixel with alternating colors and numbers
        for y in range(10):
            for x in range(10):
                if (x + y) % 2 == 0:
                    painter.fillRect(x, y, 1, 1, QColor(200, 200, 255))  # Light blue
                else:
                    painter.fillRect(x, y, 1, 1, QColor(255, 200, 200))  # Light red
        
        # Draw pixel boundaries
        painter.setPen(QColor(0, 0, 0))
        for i in range(11):
            painter.drawLine(i, 0, i, 10)  # Vertical lines
            painter.drawLine(0, i, 10, i)  # Horizontal lines
        
        painter.end()
        
        # Add to scene
        pixmap_item = QGraphicsPixmapItem(pixmap)
        self.scene.addItem(pixmap_item)
        self.scene.setSceneRect(0, 0, 10, 10)
        
    def mousePressEvent(self, event: QMouseEvent):
        """Test different rounding methods for pixel targeting."""
        if event.button() == Qt.MouseButton.RightButton:
            print("\n" + "="*60)
            print("ROUNDING METHOD COMPARISON")
            print("="*60)
            
            # Get scene coordinates
            mouse_pos = event.position()
            mouse_point = QPointF(mouse_pos.x(), mouse_pos.y())
            scene_pos = self.mapToScene(mouse_point.toPoint())
            
            print(f"Scene position: ({scene_pos.x():.6f}, {scene_pos.y():.6f})")
            
            # Method 1: Current method (round + 0.5)
            target_x_round = round(scene_pos.x())
            target_y_round = round(scene_pos.y())
            snapped_x_round = target_x_round + 0.5
            snapped_y_round = target_y_round + 0.5
            
            # Method 2: Floor method (floor + 0.5)
            target_x_floor = math.floor(scene_pos.x())
            target_y_floor = math.floor(scene_pos.y())
            snapped_x_floor = target_x_floor + 0.5
            snapped_y_floor = target_y_floor + 0.5
            
            # Method 3: Truncate method (int + 0.5)
            target_x_trunc = int(scene_pos.x())
            target_y_trunc = int(scene_pos.y())
            snapped_x_trunc = target_x_trunc + 0.5
            snapped_y_trunc = target_y_trunc + 0.5
            
            # Method 4: Proper pixel boundary detection
            # If we're in the left half of a pixel, target that pixel
            # If we're in the right half, target the next pixel
            frac_x = scene_pos.x() - math.floor(scene_pos.x())
            frac_y = scene_pos.y() - math.floor(scene_pos.y())
            
            if frac_x < 0.5:
                target_x_proper = math.floor(scene_pos.x())
            else:
                target_x_proper = math.floor(scene_pos.x()) + 1
                
            if frac_y < 0.5:
                target_y_proper = math.floor(scene_pos.y())
            else:
                target_y_proper = math.floor(scene_pos.y()) + 1
                
            snapped_x_proper = target_x_proper + 0.5
            snapped_y_proper = target_y_proper + 0.5
            
            print(f"\nTARGET PIXEL COMPARISON:")
            print(f"Method 1 (round):  pixel ({target_x_round}, {target_y_round}), snapped ({snapped_x_round:.1f}, {snapped_y_round:.1f})")
            print(f"Method 2 (floor):  pixel ({target_x_floor}, {target_y_floor}), snapped ({snapped_x_floor:.1f}, {snapped_y_floor:.1f})")
            print(f"Method 3 (trunc):  pixel ({target_x_trunc}, {target_y_trunc}), snapped ({snapped_x_trunc:.1f}, {snapped_y_trunc:.1f})")
            print(f"Method 4 (proper): pixel ({target_x_proper}, {target_y_proper}), snapped ({snapped_x_proper:.1f}, {snapped_y_proper:.1f})")
            
            print(f"\nFRACTIONAL PARTS:")
            print(f"X fraction: {frac_x:.3f} ({'left half' if frac_x < 0.5 else 'right half'} of pixel)")
            print(f"Y fraction: {frac_y:.3f} ({'top half' if frac_y < 0.5 else 'bottom half'} of pixel)")
            
            # Show which pixel the user is actually over
            actual_pixel_x = math.floor(scene_pos.x())
            actual_pixel_y = math.floor(scene_pos.y())
            print(f"\nACTUAL PIXEL UNDER CURSOR: ({actual_pixel_x}, {actual_pixel_y})")
            
            # Test all methods and show which one matches the actual pixel
            methods = [
                ("round", target_x_round, target_y_round),
                ("floor", target_x_floor, target_y_floor),
                ("trunc", target_x_trunc, target_y_trunc),
                ("proper", target_x_proper, target_y_proper)
            ]
            
            print(f"\nMETHOD ACCURACY:")
            for name, tx, ty in methods:
                correct_x = tx == actual_pixel_x
                correct_y = ty == actual_pixel_y
                accuracy = "✓" if (correct_x and correct_y) else "✗"
                print(f"{name:>6}: {accuracy} (targets {tx}, {ty})")
            
            # Place crosshairs using the floor method (most accurate)
            snapped_pos = QPointF(snapped_x_floor, snapped_y_floor)
            self.place_crosshairs(snapped_pos)
            
        super().mousePressEvent(event)
        
    def place_crosshairs(self, scene_pos: QPointF):
        """Place crosshairs at the specified scene position."""
        # Remove existing crosshairs
        self.remove_crosshairs()
        
        # Get scene bounds
        scene_rect = self.scene.sceneRect()
        
        # Create crosshair pen
        line_pen = QPen(QColor(255, 0, 0, 200))  # Red, slightly transparent
        line_pen.setWidthF(0.05)  # Very thin lines for precision
        line_pen.setStyle(Qt.PenStyle.SolidLine)
        
        # Create horizontal line
        self.crosshair_horizontal = QGraphicsLineItem(
            scene_rect.left(), scene_pos.y(),
            scene_rect.right(), scene_pos.y()
        )
        self.crosshair_horizontal.setPen(line_pen)
        self.crosshair_horizontal.setZValue(1000)
        self.scene.addItem(self.crosshair_horizontal)
        
        # Create vertical line
        self.crosshair_vertical = QGraphicsLineItem(
            scene_pos.x(), scene_rect.top(),
            scene_pos.x(), scene_rect.bottom()
        )
        self.crosshair_vertical.setPen(line_pen)
        self.crosshair_vertical.setZValue(1000)
        self.scene.addItem(self.crosshair_vertical)
        
        # Create center square outline
        square_pen = QPen(QColor(255, 0, 0, 255))  # Solid red
        square_pen.setWidthF(0.02)  # Ultra-thin outline
        
        self.crosshair_center_square = QGraphicsRectItem(
            scene_pos.x() - 0.5, scene_pos.y() - 0.5, 1.0, 1.0
        )
        self.crosshair_center_square.setPen(square_pen)
        from PyQt6.QtGui import QBrush
        self.crosshair_center_square.setBrush(QBrush(Qt.BrushStyle.NoBrush))
        self.crosshair_center_square.setZValue(1001)
        self.scene.addItem(self.crosshair_center_square)
        
    def remove_crosshairs(self):
        """Remove existing crosshairs from the scene."""
        if self.crosshair_horizontal and self.scene:
            self.scene.removeItem(self.crosshair_horizontal)
            self.crosshair_horizontal = None
            
        if self.crosshair_vertical and self.scene:
            self.scene.removeItem(self.crosshair_vertical)
            self.crosshair_vertical = None
            
        if self.crosshair_center_square and self.scene:
            self.scene.removeItem(self.crosshair_center_square)
            self.crosshair_center_square = None

class RoundingTestWindow(QWidget):
    """Main window for rounding method testing."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Pixel Rounding Method Test")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # Instructions
        instructions = QLabel("""
Pixel Rounding Method Test

This tests different rounding methods to find the correct pixel targeting approach.

Instructions:
1. Right-click on different parts of pixels (center, edges, corners)
2. Check the console output to see which method correctly identifies the pixel
3. The red crosshairs show the result using the floor method

The grid is zoomed 20x. Each colored square represents one pixel.
Light blue and light red squares alternate in a checkerboard pattern.
        """)
        instructions.setWordWrap(True)
        layout.addWidget(instructions)
        
        # Test view
        self.test_view = RoundingTestView()
        layout.addWidget(self.test_view)
        
        self.setLayout(layout)

def main():
    """Run the rounding method test."""
    app = QApplication(sys.argv)
    
    window = RoundingTestWindow()
    window.show()
    
    print("Pixel Rounding Method Test Started")
    print("Right-click on different parts of pixels to test rounding methods")
    print("Check which method correctly identifies the pixel under the cursor")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
